{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\tenant\\index.vue", "mtime": 1754556825919}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _downloadDynaTemplate = _interopRequireDefault(require(\"./downloadDynaTemplate\"));\nvar _deliveryPlan = require(\"@/api/dm/deliveryPlan\");\nvar _dian = _interopRequireDefault(require(\"@/utils/dian\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  name: \"dm-deliveryPlan-tenant\",\n  components: {\n    Download: _downloadDynaTemplate.default\n  },\n  data: function data() {\n    return {\n      PlanDeleteFlagOptions: _store.default.getters.commonEnums['dm.DeliveryPlanDeleteFlagEnum'],\n      // 订单类型\n      ValidTypeOptions: _store.default.getters.commonEnums['comm.ValidEnum'],\n      queryParam: {\n        page: 1,\n        limit: 20,\n        planNo: '',\n        tenantIds: 'tenantIds',\n        //用于区分是否为供应商\n        orderNo: '',\n        // 订单号\n        sortObj: 'dd.id DESC',\n        //排序\n        whereType: 1,\n        //气泡查询条件 默认为1 - 全部\n        vendor: '',\n        // 供应商编码|名称\n        goods: '',\n        // 物料编码|名称|规格\n        keyword: '',\n        // 物料编码|名称|描述|图号\n        queryDate: '',\n        //要求送货日期\n        startDate: '',\n        // 开始日期\n        orderType: '',\n        // 订单类型\n        validType: '',\n        creater: '',\n        // 创建人\n        deptId: '',\n        //组织机构id\n        deleteFlag: '5' //排序方式(1-全部；2-按物料；3-按可制单数量>0)\n      },\n\n      planItemFromVisible: false,\n      queryDates: [],\n      //要求送货日期\n      deliveryCount: {},\n      //气泡数\n      listLoading: false,\n      btnLoading: false,\n      formVisible: false,\n      list: [],\n      //列表数据\n      hisList: [],\n      //历史列表数据\n      tabIdx: 1,\n      total: 0,\n      //条数\n      hisTotal: 0,\n      selectedDatas: [],\n      /*选择的数据*/\n      selectedNum: 0,\n      /*选择数据的条数*/\n      userInfo: _store.default.getters.userInfo,\n      /*获取当前用户*/\n      dataForm: {\n        id: '',\n        saleNo: '',\n        deptName: '',\n        vendorName: '',\n        goodsErpCode: '',\n        goodsName: '',\n        goodsModel: '',\n        matchNum: '',\n        makeNum: '',\n        unCompetentNum: '',\n        refundNum: '',\n        planDate: ''\n      },\n      buttonFrom: {\n        count1: 'primary',\n        count2: '',\n        count3: '',\n        count4: '',\n        count5: '',\n        count6: ''\n      }\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  watch: {\n    $route: {\n      handler: function handler(to, from) {\n        if (to.path === \"/dm/deliveryPlan/tenant\" && to.query) {\n          this.queryParam.vendor = to.query.vendorCode;\n          if (to.query.planDate) {\n            var planDate = new Date(to.query.planDate);\n            this.queryDates = [planDate, planDate];\n          }\n          this.initData();\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    initData: function initData() {\n      var _this = this;\n      if (this.tabIdx === 1) {\n        this.listLoading = true;\n        if (this.queryDates.length !== 0) {\n          var startDate = this.$dian.dateFormat(this.queryDates[0], 'YYYY-MM-DD');\n          var endDate = this.$dian.dateFormat(this.queryDates[1], 'YYYY-MM-DD');\n          this.queryParam.queryDate = startDate + \" 至 \" + endDate;\n        }\n        var subCompanyInfoData = _dian.default.storageGet('subCompanyInfo');\n        if (subCompanyInfoData) {\n          this.queryParam.deptId = subCompanyInfoData.id;\n        }\n        (0, _deliveryPlan.getDeliveryPlanList)(this.queryParam).then(function (res) {\n          _this.total = res.data.totalCount;\n          _this.list = res.data.list;\n          _this.listLoading = false;\n        }).catch(function () {\n          _this.listLoading = false;\n        });\n      }\n      if (this.tabIdx === 2) {\n        this.initHisData();\n      }\n    },\n    // 历史数据\n    initHisData: function initHisData() {\n      var _this2 = this;\n      this.listLoading = true;\n      if (this.queryDates.length !== 0) {\n        var startDate = this.$dian.dateFormat(this.queryDates[0], 'YYYY-MM-DD');\n        var endDate = this.$dian.dateFormat(this.queryDates[1], 'YYYY-MM-DD');\n        this.queryParam.queryDate = startDate + \" 至 \" + endDate;\n      }\n      var subCompanyInfoData = _dian.default.storageGet('subCompanyInfo');\n      if (subCompanyInfoData) {\n        this.queryParam.deptId = subCompanyInfoData.id;\n      }\n      console.log(this.queryParam);\n      (0, _deliveryPlan.queryDeliveryPlanHisPage)(this.queryParam).then(function (res) {\n        _this2.total = res.data.totalCount;\n        _this2.hisList = res.data.list;\n        _this2.listLoading = false;\n      }).catch(function () {\n        _this2.listLoading = false;\n      });\n    },\n    //点击气泡查询\n    changeCountsButton: function changeCountsButton(stat, name) {\n      // 动态变换\n      this.buttonFrom.count1 = '';\n      this.buttonFrom.count2 = '';\n      this.buttonFrom[name] = 'primary';\n      if (stat === 1) {\n        this.tabIdx = 1;\n        this.search();\n      }\n      if (stat === 2) {\n        this.tabIdx = 2;\n        this.searchHis();\n      }\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n      //获取所有选中项数组的长度\n      this.selectedNum = selection.length;\n    },\n    importHandle: function importHandle() {\n      this.search();\n    },\n    // 搜索方法，并返回到第一页\n    search: function search() {\n      this.queryParam.page = 1;\n      this.initData();\n    },\n    searchHis: function searchHis() {\n      this.queryParam.page = 1;\n      this.initHisData();\n    },\n    // 重置方法\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.queryDates = [];\n      this.search();\n    },\n    //打开送货单详情弹窗\n    openInfoForm: function openInfoForm(id) {\n      var _this3 = this;\n      this.dataForm = this.$options.data().dataForm;\n      this.planItemFromVisible = true;\n      if (id) {\n        (0, _deliveryPlan.getDeliveryPlanItemInfo)(id).then(function (res) {\n          _this3.dataForm = res.data;\n        });\n      }\n    },\n    //关闭刷新列表数据\n    callDeliveryBoardList: function callDeliveryBoardList() {\n      this.formVisible = false;\n      this.search();\n    },\n    // 快速跳转至计划交期确认报表\n    openReplyReport: function openReplyReport() {\n      this.$router.push({\n        path: '/dm/report/planReplyReport'\n      });\n    },\n    //导出\n    exportHandle: function exportHandle() {\n      this.$refs.export.init('/api/dm/deliveryPlanItem/export', '送货计划', this.queryParam);\n    }\n  }\n};\nexports.default = _default;", null]}